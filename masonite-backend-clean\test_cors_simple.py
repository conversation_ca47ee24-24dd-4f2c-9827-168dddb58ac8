#!/usr/bin/env python3
"""
Simple CORS Test
Test CORS headers are properly returned
"""

import requests
import json
import sys

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_cors_headers():
    """Test CORS headers on different endpoints"""
    print("🔍 Testing CORS Headers")
    print("=" * 50)
    
    endpoints = [
        "/auth/login",
        "/payments/test",
        "/payments/create-order"
    ]
    
    for endpoint in endpoints:
        print(f"\n📍 Testing endpoint: {endpoint}")
        
        # Test preflight OPTIONS request
        try:
            headers = {
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type,Authorization"
            }
            
            options_response = requests.options(f"{API_BASE}{endpoint}", headers=headers, timeout=5)
            print(f"   OPTIONS Status: {options_response.status_code}")
            
            cors_headers = {
                "Access-Control-Allow-Origin": options_response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Methods": options_response.headers.get("Access-Control-Allow-Methods"),
                "Access-Control-Allow-Headers": options_response.headers.get("Access-Control-Allow-Headers"),
                "Access-Control-Allow-Credentials": options_response.headers.get("Access-Control-Allow-Credentials")
            }
            
            print(f"   CORS Headers: {cors_headers}")
            
            # Test actual GET request
            get_response = requests.get(f"{API_BASE}{endpoint}", 
                                      headers={"Origin": "http://localhost:3000"}, 
                                      timeout=5)
            print(f"   GET Status: {get_response.status_code}")
            
            get_cors_headers = {
                "Access-Control-Allow-Origin": get_response.headers.get("Access-Control-Allow-Origin"),
                "Access-Control-Allow-Credentials": get_response.headers.get("Access-Control-Allow-Credentials")
            }
            print(f"   GET CORS Headers: {get_cors_headers}")
            
        except Exception as e:
            print(f"   ❌ Error testing {endpoint}: {str(e)}")

def test_simple_request():
    """Test a simple request to verify server is working"""
    print(f"\n🧪 Testing simple request...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        return response.status_code == 200
    except Exception as e:
        print(f"   Error: {str(e)}")
        return False

def main():
    """Run CORS tests"""
    print("🚀 CORS Headers Test")
    print("=" * 60)
    
    # Test server is responding
    server_ok = test_simple_request()
    if not server_ok:
        print("❌ Server not responding")
        return 1
    
    # Test CORS headers
    test_cors_headers()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
