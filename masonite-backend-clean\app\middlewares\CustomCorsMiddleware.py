"""Custom CORS Middleware for handling Cross-Origin Resource Sharing"""

from masonite.middleware import Middleware
from masonite.request import Request
from masonite.response import Response


class CustomCorsMiddleware(Middleware):
    """Custom CORS middleware that actually works"""
    
    def before(self, request: Request, response: Response):
        """Handle CORS headers before request processing"""
        
        # Get origin from request
        origin = request.header('Origin')
        
        # Set CORS headers
        response.header('Access-Control-Allow-Origin', '*')
        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Max-Age', '86400')
        
        # Handle preflight OPTIONS requests
        if request.get_request_method().upper() == 'OPTIONS':
            return response.json({}, 204)
        
        return request
    
    def after(self, request: Request, response: Response):
        """Ensure CORS headers are set after request processing"""
        
        # Make sure CORS headers are always present
        if not response.get_header('Access-Control-Allow-Origin'):
            response.header('Access-Control-Allow-Origin', '*')
            response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
            response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With, Accept')
            response.header('Access-Control-Allow-Credentials', 'true')
        
        return request
