#!/usr/bin/env python3
"""
Debug Payment Creation Issue
Test payment creation endpoint with proper authentication
"""

import requests
import json
import sys
import time
import random

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_payment_creation_debug():
    """Debug payment creation endpoint"""
    print("🔍 Debug Payment Creation Issue")
    print("=" * 50)
    
    # Step 1: Register a fresh user with unique email
    timestamp = int(time.time())
    random_id = random.randint(1000, 9999)
    
    register_data = {
        "name": f"Payment Tester {timestamp}",
        "email": f"payment.test.{timestamp}.{random_id}@example.com",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    print(f"1️⃣ Registering fresh user...")
    print(f"   Email: {register_data['email']}")
    
    try:
        register_response = requests.post(f"{API_BASE}/auth/register", 
                                        json=register_data,
                                        headers={"Content-Type": "application/json"},
                                        timeout=10)
        
        print(f"   Status: {register_response.status_code}")
        
        if register_response.status_code != 201:
            print(f"   ❌ Registration failed: {register_response.text}")
            return False
            
        register_result = register_response.json()
        token = register_result.get('token')
        user = register_result.get('user')
        
        print(f"   ✅ User registered successfully")
        print(f"   User ID: {user.get('id')}")
        print(f"   Token: {token[:20]}..." if token else "   ❌ No token received")
        
        if not token:
            print("   ❌ No authentication token received")
            return False
            
    except Exception as e:
        print(f"   ❌ Registration error: {str(e)}")
        return False
    
    # Step 2: Test payment creation
    print(f"\n2️⃣ Testing payment creation...")
    
    payment_data = {
        "amount": 100.50,
        "currency": "INR",
        "description": "Test payment creation debug"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"   Payment data: {json.dumps(payment_data, indent=2)}")
    print(f"   Auth header: Bearer {token[:20]}...")
    
    try:
        payment_response = requests.post(f"{API_BASE}/payments/create-order", 
                                       json=payment_data, 
                                       headers=headers,
                                       timeout=10)
        
        print(f"   Status: {payment_response.status_code}")
        print(f"   Response Headers: {dict(payment_response.headers)}")
        print(f"   Response Body: {payment_response.text}")
        
        if payment_response.status_code == 200:
            try:
                payment_result = payment_response.json()
                print(f"   ✅ Payment creation successful!")
                print(f"   Order ID: {payment_result.get('orderId', 'N/A')}")
                print(f"   Amount: {payment_result.get('amount', 'N/A')}")
                return True
            except:
                print(f"   ❌ Could not parse payment response as JSON")
                return False
        else:
            print(f"   ❌ Payment creation failed")
            if payment_response.status_code == 422:
                try:
                    error_data = payment_response.json()
                    print(f"   Validation Error: {error_data}")
                except:
                    pass
            return False
            
    except Exception as e:
        print(f"   ❌ Payment creation error: {str(e)}")
        return False

def test_payment_test_endpoint():
    """Test the simple payment test endpoint"""
    print(f"\n3️⃣ Testing payment test endpoint...")
    
    try:
        response = requests.get(f"{API_BASE}/payments/test", timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"   Error: {str(e)}")
        return False

def main():
    """Run debug tests"""
    print("🚀 Payment Creation Debug Test")
    print("=" * 60)
    
    # Test simple payment endpoint first
    test_ok = test_payment_test_endpoint()
    
    # Test payment creation
    payment_ok = test_payment_creation_debug()
    
    print("\n" + "=" * 60)
    print("📊 DEBUG RESULTS:")
    print(f"   Payment Test Endpoint: {'✅ OK' if test_ok else '❌ FAIL'}")
    print(f"   Payment Creation: {'✅ OK' if payment_ok else '❌ FAIL'}")
    
    if payment_ok:
        print("\n🎉 SUCCESS: Payment creation is working!")
        return 0
    else:
        print("\n❌ FAILED: Payment creation still has issues")
        return 1

if __name__ == "__main__":
    sys.exit(main())
