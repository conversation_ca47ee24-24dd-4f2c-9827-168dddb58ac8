"""Payment Controller for Razorpay payment processing"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response
from masonite.validation import Validator
from datetime import datetime
from app.services.PaymentService import PaymentService
from app.models.Payment import Payment


class PaymentController(Controller):
    """Handle payment operations with Razorpay integration"""
    
    def __init__(self):
        """Initialize payment service"""
        self.payment_service = PaymentService()
    
    def create_order(self, request: Request, response: Response):
        """
        Create a new payment order
        POST /api/payments/create-order
        """
        print(f"🚀 PaymentController.create_order - Method started")
        print(f"🚀 PaymentController.create_order - Request method: {request.get_request_method()}")
        print(f"🚀 PaymentController.create_order - Request path: {request.get_path()}")
        
        try:
            print(f"🚀 PaymentController.create_order - Entering try block")
            # Simple validation without problematic rules
            amount = request.input('amount')
            currency = request.input('currency')
            description = request.input('description', 'Payment for services')
            
            print(f"🚀 PaymentController.create_order - Got inputs: amount={amount}, currency={currency}")
            
            # Manual validation
            if not amount:
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError', 
                        'message': 'Amount is required'
                    }
                }, 422)
            
            try:
                amount = float(amount)
                if amount <= 0 or amount > 1000000:
                    raise ValueError("Invalid amount range")
            except (ValueError, TypeError):
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError', 
                        'message': 'Amount must be a valid number between 1 and 1000000'
                    }
                }, 422)
            
            if not currency or currency not in ['INR', 'USD']:
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError', 
                        'message': 'Currency must be INR or USD'
                    }
                }, 422)
            
            # Get authenticated user from request (set by JWT middleware)
            print(f"🔍 PaymentController - Getting user from request...")
            
            # The JWT middleware should have set the user on the request
            user = request.user()
            print(f"🔍 PaymentController - request.user(): {user}")
            print(f"🔍 PaymentController - User type: {type(user)}")
            
            if user:
                print(f"🔍 PaymentController - User ID: {user.id}")
                print(f"🔍 PaymentController - User email: {user.email}")
            else:
                print(f"❌ PaymentController - No user found in request")
                print(f"❌ PaymentController - This suggests JWT middleware didn't run or failed")
                return response.json({'error': 'Authentication required'}, 401)
            
            # Create payment order
            order_data = self.payment_service.create_order(
                amount=amount,
                currency=currency,
                user_id=user.id,
                description=description
            )
            
            return response.json(order_data, 200)
            
        except Exception as e:
            print(f"❌ Create order error: {str(e)}")
            import traceback
            traceback.print_exc()
            return response.json({
                'error': 'Failed to create payment order',
                'message': str(e)
            }, 500)
    
    def test(self, request: Request, response: Response):
        """
        Test endpoint to verify PaymentController is working
        GET /api/payments/test
        """
        print(f"🧪 PaymentController.test - Test method called")
        return response.json({
            'status': 'success',
            'message': 'PaymentController is working',
            'controller': 'PaymentController',
            'method': 'test'
        }, 200)
    
    def verify_payment(self, request: Request, response: Response):
        """
        Verify payment signature
        POST /api/payments/verify
        """
        try:
            # Extract request data
            order_id = request.input('orderId')
            payment_id = request.input('paymentId')
            signature = request.input('signature')
            
            # Simple validation
            if not order_id or not payment_id or not signature:
                return response.json({
                    'error': 'orderId, paymentId, and signature are required'
                }, 400)
            
            # Verify payment
            is_valid = self.payment_service.verify_payment(order_id, payment_id, signature)
            
            if is_valid:
                return response.json({
                    'success': True,
                    'message': 'Payment verified successfully'
                }, 200)
            else:
                return response.json({
                    'success': False,
                    'message': 'Payment verification failed'
                }, 400)
                
        except Exception as e:
            print(f"❌ Payment verification error: {str(e)}")
            return response.json({
                'error': 'Payment verification failed',
                'message': str(e)
            }, 500)
    
    def get_payment_status(self, request: Request, response: Response):
        """
        Get payment status by order ID
        GET /api/payments/status/{order_id}
        """
        try:
            order_id = request.param('order_id')
            if not order_id:
                return response.json({'error': 'Order ID is required'}, 400)
            
            payment = self.payment_service.get_payment_status(order_id)
            
            if payment:
                return response.json({
                    'id': payment.id,
                    'orderId': payment.razorpay_order_id,
                    'paymentId': payment.razorpay_payment_id,
                    'amount': payment.amount,
                    'currency': payment.currency,
                    'status': payment.status,
                    'description': payment.description,
                    'createdAt': payment.created_at.isoformat() if payment.created_at else None,
                    'paidAt': payment.paid_at.isoformat() if payment.paid_at else None
                }, 200)
            else:
                return response.json({'error': 'Payment not found'}, 404)
                
        except Exception as e:
            print(f"❌ Get payment status error: {str(e)}")
            return response.json({
                'error': 'Failed to get payment status',
                'message': str(e)
            }, 500)
    
    def get_user_payments(self, request: Request, response: Response):
        """
        Get all payments for authenticated user with pagination and filtering
        GET /api/payments/user?page=1&limit=10&status=paid&from_date=2024-01-01&to_date=2024-12-31
        """
        try:
            user = request.user()
            if not user:
                return response.json({'error': 'Authentication required'}, 401)

            # Get pagination parameters
            page = int(request.input('page', 1))
            limit = min(int(request.input('limit', 20)), 100)  # Max 100 per page
            offset = (page - 1) * limit

            # Get filter parameters
            status_filter = request.input('status')
            from_date = request.input('from_date')
            to_date = request.input('to_date')

            # Get payments with filters
            payments = self.payment_service.get_user_payments(
                user.id,
                limit=limit,
                offset=offset,
                status_filter=status_filter,
                from_date=from_date,
                to_date=to_date
            )

            # Get total count for pagination
            total_count = self.payment_service.get_user_payments_count(
                user.id,
                status_filter=status_filter,
                from_date=from_date,
                to_date=to_date
            )

            payment_list = []
            for payment in payments:
                payment_summary = payment.get_payment_summary()
                # Add refund information
                refunds = payment.refunds().get() if hasattr(payment, 'refunds') else []
                payment_summary['refunds'] = [refund.get_refund_summary() for refund in refunds]
                payment_list.append(payment_summary)

            return response.json({
                'payments': payment_list,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count,
                    'totalPages': (total_count + limit - 1) // limit,
                    'hasNext': offset + limit < total_count,
                    'hasPrev': page > 1
                },
                'summary': {
                    'totalPayments': total_count,
                    'totalAmount': sum(float(p.amount) for p in payments),
                    'totalRefunded': sum(float(p.refunded_amount or 0) for p in payments)
                }
            }, 200)

        except Exception as e:
            print(f"❌ Get user payments error: {str(e)}")
            return response.json({
                'error': 'Failed to get user payments',
                'message': str(e)
            }, 500)
    
    def refund_payment(self, request: Request, response: Response):
        """
        Process payment refund with enhanced validation and tracking
        POST /api/payments/refund
        Body: {
            "paymentId": "pay_xxx",
            "amount": 100.50,  // Optional for partial refund
            "reason": "Customer request"
        }
        """
        try:
            user = request.user()
            if not user:
                return response.json({'error': 'Authentication required'}, 401)

            payment_id = request.input('paymentId')
            amount = request.input('amount')  # Optional partial refund
            reason = request.input('reason', 'Refund requested by customer')

            if not payment_id:
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'Payment ID is required'
                    }
                }, 422)

            # Validate refund amount if provided
            refund_amount = None
            if amount:
                try:
                    refund_amount = float(amount)
                    if refund_amount <= 0:
                        return response.json({
                            'error': {
                                'statusCode': 422,
                                'name': 'ValidationError',
                                'message': 'Refund amount must be greater than 0'
                            }
                        }, 422)
                except (ValueError, TypeError):
                    return response.json({
                        'error': {
                            'statusCode': 422,
                            'name': 'ValidationError',
                            'message': 'Invalid refund amount format'
                        }
                    }, 422)

            # Verify payment belongs to user (security check)
            from app.models.Payment import Payment
            payment = Payment.where('razorpay_payment_id', payment_id).first()
            if not payment:
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFoundError',
                        'message': 'Payment not found'
                    }
                }, 404)

            if payment.user_id != user.id:
                return response.json({
                    'error': {
                        'statusCode': 403,
                        'name': 'ForbiddenError',
                        'message': 'You can only refund your own payments'
                    }
                }, 403)

            # Process refund
            success = self.payment_service.refund_payment(payment_id, refund_amount, reason)

            if success:
                # Get updated payment info
                updated_payment = Payment.where('razorpay_payment_id', payment_id).first()

                return response.json({
                    'success': True,
                    'message': 'Refund processed successfully',
                    'refund': {
                        'paymentId': payment_id,
                        'refundAmount': refund_amount or float(payment.remaining_refund_amount()),
                        'totalRefunded': float(updated_payment.refunded_amount or 0),
                        'remainingAmount': float(updated_payment.remaining_refund_amount()),
                        'status': updated_payment.status,
                        'reason': reason
                    }
                }, 200)
            else:
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'RefundError',
                        'message': 'Refund processing failed. Please check payment status and refund eligibility.'
                    }
                }, 400)

        except Exception as e:
            print(f"❌ Refund payment error: {str(e)}")
            return response.json({
                'error': {
                    'statusCode': 500,
                    'name': 'InternalServerError',
                    'message': 'Refund processing failed due to server error'
                }
            }, 500)

    def get_payment_analytics(self, request: Request, response: Response):
        """
        Get payment analytics for authenticated user
        GET /api/payments/analytics?period=30d
        """
        try:
            user = request.user()
            if not user:
                return response.json({'error': 'Authentication required'}, 401)

            period = request.input('period', '30d')  # 7d, 30d, 90d, 1y

            analytics = self.payment_service.get_payment_analytics(user.id, period)

            return response.json({
                'analytics': analytics,
                'period': period,
                'generatedAt': datetime.now().isoformat()
            }, 200)

        except Exception as e:
            print(f"❌ Get payment analytics error: {str(e)}")
            return response.json({
                'error': 'Failed to get payment analytics',
                'message': str(e)
            }, 500)

    def get_refund_history(self, request: Request, response: Response):
        """
        Get refund history for authenticated user
        GET /api/payments/refunds?page=1&limit=10
        """
        try:
            user = request.user()
            if not user:
                return response.json({'error': 'Authentication required'}, 401)

            page = int(request.input('page', 1))
            limit = min(int(request.input('limit', 20)), 100)
            offset = (page - 1) * limit

            refunds = self.payment_service.get_user_refunds(user.id, limit, offset)
            total_count = self.payment_service.get_user_refunds_count(user.id)

            refund_list = []
            for refund in refunds:
                refund_summary = refund.get_refund_summary()
                # Add payment info
                payment = refund.payment().first()
                if payment:
                    refund_summary['payment'] = {
                        'orderId': payment.razorpay_order_id,
                        'amount': float(payment.amount),
                        'description': payment.description
                    }
                refund_list.append(refund_summary)

            return response.json({
                'refunds': refund_list,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total_count,
                    'totalPages': (total_count + limit - 1) // limit,
                    'hasNext': offset + limit < total_count,
                    'hasPrev': page > 1
                }
            }, 200)

        except Exception as e:
            print(f"❌ Get refund history error: {str(e)}")
            return response.json({
                'error': 'Failed to get refund history',
                'message': str(e)
            }, 500)

    def cancel_payment(self, request: Request, response: Response):
        """
        Cancel a pending payment
        POST /api/payments/cancel
        Body: {"orderId": "order_xxx", "reason": "Customer request"}
        """
        try:
            user = request.user()
            if not user:
                return response.json({'error': 'Authentication required'}, 401)

            order_id = request.input('orderId')
            reason = request.input('reason', 'Cancelled by customer')

            if not order_id:
                return response.json({
                    'error': {
                        'statusCode': 422,
                        'name': 'ValidationError',
                        'message': 'Order ID is required'
                    }
                }, 422)

            # Find payment
            from app.models.Payment import Payment
            payment = Payment.where('razorpay_order_id', order_id).first()

            if not payment:
                return response.json({
                    'error': {
                        'statusCode': 404,
                        'name': 'NotFoundError',
                        'message': 'Payment not found'
                    }
                }, 404)

            if payment.user_id != user.id:
                return response.json({
                    'error': {
                        'statusCode': 403,
                        'name': 'ForbiddenError',
                        'message': 'You can only cancel your own payments'
                    }
                }, 403)

            if not payment.is_pending():
                return response.json({
                    'error': {
                        'statusCode': 400,
                        'name': 'InvalidStateError',
                        'message': 'Only pending payments can be cancelled'
                    }
                }, 400)

            # Cancel payment
            payment.mark_as_cancelled(reason)

            return response.json({
                'success': True,
                'message': 'Payment cancelled successfully',
                'payment': payment.get_payment_summary()
            }, 200)

        except Exception as e:
            print(f"❌ Cancel payment error: {str(e)}")
            return response.json({
                'error': 'Failed to cancel payment',
                'message': str(e)
            }, 500)
    
    def webhook(self, request: Request, response: Response):
        """
        Handle Razorpay webhook events
        POST /api/payments/webhook
        """
        try:
            payload = request.all()
            signature = request.header('X-Razorpay-Signature')
            
            if not signature:
                return response.json({'error': 'Missing webhook signature'}, 400)
            
            # Handle webhook
            success = self.payment_service.handle_webhook(payload, signature)
            
            if success:
                return response.json({'status': 'processed'}, 200)
            else:
                return response.json({'error': 'Webhook processing failed'}, 400)
                
        except Exception as e:
            print(f"❌ Webhook error: {str(e)}")
            return response.json({
                'error': 'Webhook processing failed',
                'message': str(e)
            }, 500)

class PaymentWebhookController(Controller):
    """Handle Razorpay webhook events (unauthenticated)"""
    
    def __init__(self):
        """Initialize payment service"""
        self.payment_service = PaymentService()
    
    def handle_webhook(self, request: Request, response: Response):
        """
        Handle Razorpay webhook
        POST /api/payments/webhook
        """
        try:
            # Get webhook signature from headers (Razorpay uses x-razorpay-signature)
            signature = request.header('x-razorpay-signature')
            
            # Get raw payload for signature verification
            payload = request.all()
            
            print(f"📨 Received webhook:")
            print(f"   - Signature header: {'Present' if signature else 'Missing'}")
            print(f"   - Payload keys: {list(payload.keys()) if payload else 'None'}")
            print(f"   - Event type: {payload.get('event', 'Unknown')}")
            
            if not signature:
                print("❌ Missing x-razorpay-signature header")
                return response.json({'error': 'Missing webhook signature'}, 400)
            
            # Process webhook
            success = self.payment_service.handle_webhook(payload, signature)
            
            if success:
                print("✅ Webhook processed successfully")
                return response.json({'status': 'success'}, 200)
            else:
                print("❌ Webhook processing failed")
                return response.json({'status': 'failed'}, 400)
                
        except Exception as e:
            print(f"❌ Webhook processing error: {str(e)}")
            return response.json({
                'error': 'Webhook processing failed',
                'message': str(e)
            }, 500)
