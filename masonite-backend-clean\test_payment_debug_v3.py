#!/usr/bin/env python3
"""
Payment Debug Test v3
Debug payment creation issues
"""

import requests
import json
import sys
import time
import random

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_payment_debug():
    """Debug payment creation"""
    print("🔍 Payment Debug Test v3")
    print("=" * 50)
    
    # Generate unique test data
    timestamp = int(time.time())
    random_id = random.randint(1000, 9999)
    
    # Step 1: Register a fresh user
    print(f"\n1️⃣ Registering fresh user...")
    register_data = {
        "name": f"Payment Debug {timestamp}",
        "email": f"payment.debug.{timestamp}.{random_id}@example.com",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    try:
        register_response = requests.post(f"{API_BASE}/auth/register", 
                                        json=register_data,
                                        headers={"Content-Type": "application/json"},
                                        timeout=10)
        
        print(f"   Registration Status: {register_response.status_code}")
        
        if register_response.status_code != 201:
            print(f"   ❌ Registration failed: {register_response.text}")
            return False
            
        register_result = register_response.json()
        token = register_result.get('token')
        user = register_result.get('user')
        
        print(f"   ✅ User registered: {user.get('email')}")
        print(f"   Token: {token[:20]}..." if token else "   ❌ No token")
        
        if not token:
            return False
            
    except Exception as e:
        print(f"   ❌ Registration error: {str(e)}")
        return False
    
    # Step 2: Test payment test endpoint first
    print(f"\n2️⃣ Testing payment test endpoint...")
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        test_response = requests.get(f"{API_BASE}/payments/test", 
                                   headers=headers, timeout=10)
        
        print(f"   Test endpoint status: {test_response.status_code}")
        print(f"   Test endpoint response: {test_response.text}")
        
    except Exception as e:
        print(f"   ❌ Test endpoint error: {str(e)}")
    
    # Step 3: Test payment creation with detailed logging
    print(f"\n3️⃣ Testing payment creation...")
    payment_data = {
        "amount": 50.0,
        "currency": "INR",
        "description": "Debug test payment"
    }
    
    try:
        print(f"   Request URL: {API_BASE}/payments/create-order")
        print(f"   Request data: {json.dumps(payment_data, indent=2)}")
        print(f"   Request headers: Authorization: Bearer {token[:20]}...")
        
        payment_response = requests.post(f"{API_BASE}/payments/create-order", 
                                       json=payment_data, 
                                       headers=headers,
                                       timeout=30)
        
        print(f"   Response status: {payment_response.status_code}")
        print(f"   Response headers: {dict(payment_response.headers)}")
        
        if payment_response.status_code == 200:
            payment_result = payment_response.json()
            print(f"   ✅ Payment created!")
            print(f"   Response: {json.dumps(payment_result, indent=2)}")
            return True
        else:
            print(f"   ❌ Payment failed")
            print(f"   Response: {payment_response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"   ❌ Connection error: {str(e)}")
        print("   This suggests the server crashed or closed the connection")
        return False
    except Exception as e:
        print(f"   ❌ Payment creation error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_server_health():
    """Test server health"""
    print(f"\n🏥 Testing server health...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"   Server status: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"   Server error: {str(e)}")
        return False

def main():
    """Run payment debug tests"""
    print("🎯 Payment Debug Test v3")
    print("=" * 60)
    
    # Test server health
    server_ok = test_server_health()
    if not server_ok:
        print("❌ Server not responding")
        return 1
    
    # Test payment
    payment_ok = test_payment_debug()
    
    print("\n" + "=" * 60)
    print("📊 DEBUG RESULTS:")
    print(f"   Server Health: {'✅ OK' if server_ok else '❌ FAIL'}")
    print(f"   Payment Creation: {'✅ OK' if payment_ok else '❌ FAIL'}")
    
    return 0 if payment_ok else 1

if __name__ == "__main__":
    sys.exit(main())
