#!/usr/bin/env python3
"""
Test Payment Creation After CORS Fix
Tests the payment creation endpoint to ensure it works after CORS configuration
"""

import requests
import json
import sys

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_payment_creation():
    """Test payment creation endpoint"""
    print("🧪 Testing Payment Creation After CORS Fix")
    print("=" * 50)
    
    # Step 1: Register a test user
    print("\n1️⃣ Registering test user...")
    register_data = {
        "name": "Payment Tester",
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    try:
        register_response = requests.post(f"{API_BASE}/auth/register", json=register_data)
        print(f"   Status: {register_response.status_code}")
        
        if register_response.status_code == 201:
            register_result = register_response.json()
            token = register_result.get('token')
            print(f"   ✅ User registered successfully")
            print(f"   Token: {token[:20]}..." if token else "   ❌ No token received")
        else:
            print(f"   ❌ Registration failed: {register_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Registration error: {str(e)}")
        return False
    
    # Step 2: Test payment creation
    print("\n2️⃣ Testing payment creation...")
    payment_data = {
        "amount": 100.50,
        "currency": "INR",
        "description": "Test payment after CORS fix"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        payment_response = requests.post(f"{API_BASE}/payments/create-order", 
                                       json=payment_data, 
                                       headers=headers)
        print(f"   Status: {payment_response.status_code}")
        print(f"   Response: {payment_response.text}")
        
        if payment_response.status_code == 200:
            payment_result = payment_response.json()
            print(f"   ✅ Payment creation successful!")
            print(f"   Order ID: {payment_result.get('orderId', 'N/A')}")
            print(f"   Amount: {payment_result.get('amount', 'N/A')}")
            return True
        else:
            print(f"   ❌ Payment creation failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Payment creation error: {str(e)}")
        return False

def test_cors_headers():
    """Test CORS headers are properly set"""
    print("\n3️⃣ Testing CORS headers...")
    
    try:
        # Test preflight request
        headers = {
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "Content-Type,Authorization"
        }
        
        options_response = requests.options(f"{API_BASE}/payments/create-order", headers=headers)
        print(f"   OPTIONS Status: {options_response.status_code}")
        
        cors_headers = {
            "Access-Control-Allow-Origin": options_response.headers.get("Access-Control-Allow-Origin"),
            "Access-Control-Allow-Methods": options_response.headers.get("Access-Control-Allow-Methods"),
            "Access-Control-Allow-Headers": options_response.headers.get("Access-Control-Allow-Headers")
        }
        
        print(f"   CORS Headers: {cors_headers}")
        
        if cors_headers["Access-Control-Allow-Origin"]:
            print(f"   ✅ CORS headers present")
            return True
        else:
            print(f"   ❌ CORS headers missing")
            return False
            
    except Exception as e:
        print(f"   ❌ CORS test error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Payment Creation Test Suite")
    print("Testing payment functionality after CORS configuration fix")
    print("=" * 60)
    
    # Test CORS headers first
    cors_ok = test_cors_headers()
    
    # Test payment creation
    payment_ok = test_payment_creation()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"   CORS Headers: {'✅ PASS' if cors_ok else '❌ FAIL'}")
    print(f"   Payment Creation: {'✅ PASS' if payment_ok else '❌ FAIL'}")
    
    if cors_ok and payment_ok:
        print("\n🎉 ALL TESTS PASSED! Payment creation works after CORS fix.")
        return 0
    else:
        print("\n❌ SOME TESTS FAILED. Check server logs for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
