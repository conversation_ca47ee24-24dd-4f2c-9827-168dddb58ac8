""" PaymentRefund Model """

from masoniteorm.models import Model
from datetime import datetime
from decimal import Decimal
from enum import Enum


class RefundStatus(Enum):
    """Refund status enumeration"""
    PENDING = 'pending'
    PROCESSED = 'processed'
    FAILED = 'failed'
    CANCELLED = 'cancelled'


class PaymentRefund(Model):
    """Payment refund model for tracking refund transactions"""

    __table__ = 'payment_refunds'
    __primary_key__ = 'id'
    __timestamps__ = True

    __fillable__ = [
        'payment_id', 'razorpay_refund_id', 'amount', 'status',
        'reason', 'notes', 'processed_by', 'processed_at',
        'failure_reason', 'metadata'
    ]

    __casts__ = {
        'amount': 'decimal:2',
        'metadata': 'json'
    }

    __dates__ = [
        'created_at', 'updated_at', 'processed_at'
    ]

    def payment(self):
        """Get the payment this refund belongs to"""
        from app.models.Payment import Payment
        return self.belongs_to(Payment, 'payment_id', 'id')

    def processed_by_user(self):
        """Get the user who processed this refund"""
        from app.models.User import User
        return self.belongs_to(User, 'processed_by', 'id')

    def is_pending(self) -> bool:
        """Check if refund is pending"""
        return self.status == RefundStatus.PENDING.value

    def is_processed(self) -> bool:
        """Check if refund is processed"""
        return self.status == RefundStatus.PROCESSED.value

    def is_failed(self) -> bool:
        """Check if refund failed"""
        return self.status == RefundStatus.FAILED.value

    def is_cancelled(self) -> bool:
        """Check if refund was cancelled"""
        return self.status == RefundStatus.CANCELLED.value

    def mark_as_processed(self, refund_id: str = None, processed_by: int = None) -> None:
        """Mark refund as processed"""
        self.status = RefundStatus.PROCESSED.value
        self.processed_at = datetime.now()
        if refund_id:
            self.razorpay_refund_id = refund_id
        if processed_by:
            self.processed_by = processed_by
        self.save()

    def mark_as_failed(self, reason: str = None) -> None:
        """Mark refund as failed"""
        self.status = RefundStatus.FAILED.value
        if reason:
            self.failure_reason = reason
        self.save()

    def mark_as_cancelled(self, reason: str = None) -> None:
        """Mark refund as cancelled"""
        self.status = RefundStatus.CANCELLED.value
        if reason:
            self.notes = reason
        self.save()

    def get_refund_summary(self) -> dict:
        """Get refund summary"""
        return {
            'id': self.id,
            'paymentId': self.payment_id,
            'razorpayRefundId': self.razorpay_refund_id,
            'amount': float(self.amount),
            'status': self.status,
            'reason': self.reason,
            'notes': self.notes,
            'processedBy': self.processed_by,
            'processedAt': self.processed_at.isoformat() if self.processed_at else None,
            'failureReason': self.failure_reason,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'isPending': self.is_pending(),
            'isProcessed': self.is_processed(),
            'isFailed': self.is_failed(),
            'isCancelled': self.is_cancelled()
        }
