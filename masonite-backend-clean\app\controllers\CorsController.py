"""CORS Controller for handling preflight requests"""

from masonite.controllers import Controller
from masonite.request import Request
from masonite.response import Response


class Cors<PERSON><PERSON>roller(Controller):
    """Handle CORS preflight requests"""
    
    def preflight(self, request: Request, response: Response):
        """
        Handle OPTIONS preflight requests for CORS
        OPTIONS /*
        """
        # Set CORS headers manually since middleware might not be working
        response.header('Access-Control-Allow-Origin', '*')
        response.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS')
        response.header('Access-Control-Allow-Headers', 'Origin, Content-Type, Authorization, X-Requested-With')
        response.header('Access-Control-Allow-Credentials', 'true')
        response.header('Access-Control-Max-Age', '86400')
        
        return response.json({}, 204)
