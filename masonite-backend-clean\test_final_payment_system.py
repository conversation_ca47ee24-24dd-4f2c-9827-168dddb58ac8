#!/usr/bin/env python3
"""
Final Payment System Test
Comprehensive test of the complete payment system functionality
"""

import requests
import json
import sys
import time
import random

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_complete_payment_flow():
    """Test complete payment flow from registration to payment creation"""
    print("🚀 Complete Payment System Test")
    print("=" * 60)
    
    # Generate unique test data
    timestamp = int(time.time())
    random_id = random.randint(1000, 9999)
    
    # Step 1: Register a fresh user
    print(f"\n1️⃣ Registering fresh user...")
    register_data = {
        "name": f"Payment Test User {timestamp}",
        "email": f"payment.final.test.{timestamp}.{random_id}@example.com",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    print(f"   Email: {register_data['email']}")
    
    try:
        register_response = requests.post(f"{API_BASE}/auth/register", 
                                        json=register_data,
                                        headers={"Content-Type": "application/json"},
                                        timeout=10)
        
        print(f"   Status: {register_response.status_code}")
        
        if register_response.status_code != 201:
            print(f"   ❌ Registration failed: {register_response.text}")
            return False
            
        register_result = register_response.json()
        token = register_result.get('token')
        user = register_result.get('user')
        
        print(f"   ✅ User registered successfully")
        print(f"   User ID: {user.get('id')}")
        print(f"   Token: {token[:20]}..." if token else "   ❌ No token received")
        
        if not token:
            print("   ❌ No authentication token received")
            return False
            
    except Exception as e:
        print(f"   ❌ Registration error: {str(e)}")
        return False
    
    # Step 2: Test payment creation
    print(f"\n2️⃣ Testing payment creation...")
    
    payment_data = {
        "amount": 250.75,
        "currency": "INR",
        "description": "Final payment system test"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        payment_response = requests.post(f"{API_BASE}/payments/create-order", 
                                       json=payment_data, 
                                       headers=headers,
                                       timeout=10)
        
        print(f"   Status: {payment_response.status_code}")
        
        if payment_response.status_code == 200:
            payment_result = payment_response.json()
            print(f"   ✅ Payment creation successful!")
            print(f"   Order ID: {payment_result.get('orderId', 'N/A')}")
            print(f"   Amount: {payment_result.get('amount', 'N/A')}")
            print(f"   Currency: {payment_result.get('currency', 'N/A')}")
            print(f"   Razorpay Key: {payment_result.get('key', 'N/A')}")
            return True
        else:
            print(f"   ❌ Payment creation failed: {payment_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Payment creation error: {str(e)}")
        return False

def test_payment_endpoints():
    """Test all payment endpoints"""
    print(f"\n3️⃣ Testing payment endpoints...")
    
    endpoints = [
        ("GET", "/payments/test", "Payment test endpoint"),
    ]
    
    for method, endpoint, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{API_BASE}{endpoint}", timeout=5)
            
            print(f"   {method} {endpoint}: {response.status_code} - {description}")
            
            if response.status_code == 200:
                print(f"      ✅ Working")
            else:
                print(f"      ❌ Issue: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Error testing {endpoint}: {str(e)}")

def test_authentication_flow():
    """Test authentication flow"""
    print(f"\n4️⃣ Testing authentication flow...")
    
    # Generate unique test data
    timestamp = int(time.time())
    random_id = random.randint(1000, 9999)
    
    # Test registration
    register_data = {
        "name": f"Auth Test User {timestamp}",
        "email": f"auth.test.{timestamp}.{random_id}@example.com",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    try:
        register_response = requests.post(f"{API_BASE}/auth/register", 
                                        json=register_data,
                                        timeout=10)
        
        print(f"   Registration: {register_response.status_code}")
        
        if register_response.status_code == 201:
            register_result = register_response.json()
            token = register_result.get('token')
            
            # Test profile endpoint
            profile_response = requests.get(f"{API_BASE}/auth/profile",
                                          headers={"Authorization": f"Bearer {token}"},
                                          timeout=10)
            
            print(f"   Profile Access: {profile_response.status_code}")
            
            if profile_response.status_code == 200:
                print(f"   ✅ Authentication flow working")
                return True
            else:
                print(f"   ❌ Profile access failed")
                return False
        else:
            print(f"   ❌ Registration failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Authentication test error: {str(e)}")
        return False

def main():
    """Run comprehensive payment system tests"""
    print("🎯 Final Payment System Validation")
    print("Testing complete payment functionality after bug fixes")
    print("=" * 70)
    
    # Test authentication flow
    auth_ok = test_authentication_flow()
    
    # Test payment endpoints
    test_payment_endpoints()
    
    # Test complete payment flow
    payment_ok = test_complete_payment_flow()
    
    print("\n" + "=" * 70)
    print("📊 FINAL TEST RESULTS:")
    print(f"   Authentication Flow: {'✅ PASS' if auth_ok else '❌ FAIL'}")
    print(f"   Payment System: {'✅ PASS' if payment_ok else '❌ FAIL'}")
    
    if auth_ok and payment_ok:
        print("\n🎉 SUCCESS: Complete payment system is working perfectly!")
        print("✅ All critical issues have been resolved")
        print("✅ Payment creation is functional")
        print("✅ Authentication is working")
        print("✅ System is ready for production")
        return 0
    else:
        print("\n❌ FAILED: Some issues remain")
        return 1

if __name__ == "__main__":
    sys.exit(main())
