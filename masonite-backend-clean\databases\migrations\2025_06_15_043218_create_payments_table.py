"""CreatePaymentsTable Migration."""

from masoniteorm.migrations import Migration


class CreatePaymentsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        # Drop table if exists to recreate with new structure
        try:
            self.schema.drop("payments")
        except:
            pass

        with self.schema.create("payments") as table:
            table.increments("id")

            # User relationship
            table.integer("user_id").unsigned()
            table.foreign("user_id").references("id").on("users").on_delete("cascade")

            # Payment provider information
            table.string("provider", 50).default("razorpay")  # razorpay, stripe, etc.
            table.string("razorpay_order_id", 100).nullable()
            table.string("razorpay_payment_id", 100).nullable()
            table.string("razorpay_signature", 255).nullable()

            # Payment details
            table.decimal("amount", 10, 2)
            table.string("currency", 3).default("INR")
            table.enum("status", ["pending", "paid", "failed", "cancelled", "refunded", "partially_refunded"]).default("pending")
            table.enum("payment_method", ["card", "upi", "netbanking", "wallet", "emi", "bank_transfer"]).nullable()

            # Payment metadata
            table.text("description").nullable()
            table.json("metadata").nullable()
            table.json("customer_info").nullable()
            table.json("billing_address").nullable()
            table.json("shipping_address").nullable()

            # Financial tracking
            table.decimal("refunded_amount", 10, 2).default(0)
            table.decimal("fees", 10, 2).default(0)
            table.decimal("tax", 10, 2).default(0)
            table.decimal("discount", 10, 2).default(0)

            # Status timestamps
            table.timestamp("paid_at").nullable()
            table.timestamp("failed_at").nullable()
            table.timestamp("cancelled_at").nullable()
            table.timestamp("refunded_at").nullable()

            # Verification and security
            table.boolean("webhook_verified").default(False)
            table.integer("risk_score").default(0)
            table.text("failure_reason").nullable()
            table.json("notes").nullable()

            # Standard timestamps
            table.timestamps()

            # Indexes for performance
            table.index("user_id")
            table.index("razorpay_order_id")
            table.index("razorpay_payment_id")
            table.index("status")
            table.index("created_at")

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("payments")
