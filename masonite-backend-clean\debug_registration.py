#!/usr/bin/env python3
"""
Debug Registration Issue
Test what data is being received by the registration endpoint
"""

import requests
import json
import sys

# Test configuration
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api"

def test_registration_debug():
    """Debug registration endpoint"""
    print("🔍 Debug Registration Issue")
    print("=" * 50)
    
    # Test data
    register_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }
    
    print(f"📤 Sending data: {json.dumps(register_data, indent=2)}")
    
    try:
        response = requests.post(f"{API_BASE}/auth/register", 
                               json=register_data,
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        print(f"📥 Response Body: {response.text}")
        
        if response.status_code == 422:
            try:
                error_data = response.json()
                print(f"🔍 Validation Error Details:")
                print(f"   Message: {error_data.get('error', {}).get('message', 'N/A')}")
                print(f"   Details: {error_data.get('error', {}).get('details', 'N/A')}")
            except:
                print("❌ Could not parse error response as JSON")
        
        return response.status_code == 201
        
    except Exception as e:
        print(f"❌ Request error: {str(e)}")
        return False

def test_simple_endpoint():
    """Test a simple endpoint to verify server is working"""
    print("\n🧪 Testing simple endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text[:100]}...")
        return response.status_code == 200
    except Exception as e:
        print(f"   Error: {str(e)}")
        return False

def main():
    """Run debug tests"""
    print("🚀 Registration Debug Test")
    print("=" * 60)
    
    # Test server is responding
    server_ok = test_simple_endpoint()
    if not server_ok:
        print("❌ Server not responding")
        return 1
    
    # Test registration
    reg_ok = test_registration_debug()
    
    print("\n" + "=" * 60)
    print("📊 DEBUG RESULTS:")
    print(f"   Server: {'✅ OK' if server_ok else '❌ FAIL'}")
    print(f"   Registration: {'✅ OK' if reg_ok else '❌ FAIL'}")
    
    return 0 if reg_ok else 1

if __name__ == "__main__":
    sys.exit(main())
