""" Payment Model """

from masoniteorm.models import Model
from datetime import datetime
from decimal import Decimal
from enum import Enum


class PaymentStatus(Enum):
    """Payment status enumeration"""
    PENDING = 'pending'
    PAID = 'paid'
    FAILED = 'failed'
    CANCELLED = 'cancelled'
    REFUNDED = 'refunded'
    PARTIALLY_REFUNDED = 'partially_refunded'


class PaymentMethod(Enum):
    """Payment method enumeration"""
    CARD = 'card'
    UPI = 'upi'
    NETBANKING = 'netbanking'
    WALLET = 'wallet'
    EMI = 'emi'
    BANK_TRANSFER = 'bank_transfer'


class Payment(Model):
    """Enhanced Payment model with comprehensive payment tracking"""

    __table__ = 'payments'
    __primary_key__ = 'id'
    __timestamps__ = True

    __fillable__ = [
        'user_id', 'provider', 'razorpay_order_id', 'razorpay_payment_id',
        'razorpay_signature', 'amount', 'currency', 'status', 'payment_method',
        'description', 'metadata', 'customer_info', 'billing_address',
        'shipping_address', 'refunded_amount', 'fees', 'tax', 'discount',
        'paid_at', 'failed_at', 'cancelled_at', 'refunded_at',
        'webhook_verified', 'risk_score', 'failure_reason', 'notes'
    ]

    __casts__ = {
        'amount': 'decimal:2',
        'refunded_amount': 'decimal:2',
        'fees': 'decimal:2',
        'tax': 'decimal:2',
        'discount': 'decimal:2',
        'metadata': 'json',
        'customer_info': 'json',
        'billing_address': 'json',
        'shipping_address': 'json',
        'notes': 'json',
        'webhook_verified': 'bool',
        'risk_score': 'int'
    }

    __dates__ = [
        'created_at', 'updated_at', 'paid_at',
        'failed_at', 'cancelled_at', 'refunded_at'
    ]

    def user(self):
        """Get the user who made this payment"""
        from app.models.User import User
        return self.belongs_to(User, 'user_id', 'id')

    def refunds(self):
        """Get all refunds for this payment"""
        from app.models.PaymentRefund import PaymentRefund
        return self.has_many(PaymentRefund, 'payment_id', 'id')

    def is_successful(self) -> bool:
        """Check if payment was successful"""
        return self.status == PaymentStatus.PAID.value

    def is_pending(self) -> bool:
        """Check if payment is pending"""
        return self.status == PaymentStatus.PENDING.value

    def is_failed(self) -> bool:
        """Check if payment failed"""
        return self.status == PaymentStatus.FAILED.value

    def is_refunded(self) -> bool:
        """Check if payment is fully refunded"""
        return self.status == PaymentStatus.REFUNDED.value

    def is_partially_refunded(self) -> bool:
        """Check if payment is partially refunded"""
        return self.status == PaymentStatus.PARTIALLY_REFUNDED.value

    def can_be_refunded(self) -> bool:
        """Check if payment can be refunded"""
        return self.is_successful() and self.remaining_refund_amount() > 0

    def remaining_refund_amount(self) -> Decimal:
        """Get remaining amount that can be refunded"""
        return Decimal(str(self.amount)) - Decimal(str(self.refunded_amount or 0))

    def total_refunded(self) -> Decimal:
        """Get total amount refunded"""
        return Decimal(str(self.refunded_amount or 0))

    def net_amount(self) -> Decimal:
        """Get net amount after refunds"""
        return self.remaining_refund_amount()

    def mark_as_paid(self, payment_id: str = None, signature: str = None) -> None:
        """Mark payment as paid"""
        self.status = PaymentStatus.PAID.value
        self.paid_at = datetime.now()
        if payment_id:
            self.razorpay_payment_id = payment_id
        if signature:
            self.razorpay_signature = signature
        self.save()

    def mark_as_failed(self, reason: str = None) -> None:
        """Mark payment as failed"""
        self.status = PaymentStatus.FAILED.value
        self.failed_at = datetime.now()
        if reason:
            self.failure_reason = reason
        self.save()

    def mark_as_cancelled(self, reason: str = None) -> None:
        """Mark payment as cancelled"""
        self.status = PaymentStatus.CANCELLED.value
        self.cancelled_at = datetime.now()
        if reason:
            notes = self.notes or {}
            notes['cancellation_reason'] = reason
            self.notes = notes
        self.save()

    def add_refund_amount(self, amount: Decimal) -> None:
        """Add to refunded amount and update status"""
        current_refunded = Decimal(str(self.refunded_amount or 0))
        new_refunded = current_refunded + amount
        self.refunded_amount = float(new_refunded)

        # Update status based on refund amount
        if new_refunded >= Decimal(str(self.amount)):
            self.status = PaymentStatus.REFUNDED.value
            self.refunded_at = datetime.now()
        else:
            self.status = PaymentStatus.PARTIALLY_REFUNDED.value

        self.save()

    def get_payment_summary(self) -> dict:
        """Get comprehensive payment summary"""
        return {
            'id': self.id,
            'orderId': self.razorpay_order_id,
            'paymentId': self.razorpay_payment_id,
            'amount': float(self.amount),
            'currency': self.currency,
            'status': self.status,
            'paymentMethod': self.payment_method,
            'description': self.description,
            'refundedAmount': float(self.refunded_amount or 0),
            'remainingAmount': float(self.remaining_refund_amount()),
            'fees': float(self.fees or 0),
            'tax': float(self.tax or 0),
            'discount': float(self.discount or 0),
            'webhookVerified': self.webhook_verified,
            'riskScore': self.risk_score,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'paidAt': self.paid_at.isoformat() if self.paid_at else None,
            'failedAt': self.failed_at.isoformat() if self.failed_at else None,
            'cancelledAt': self.cancelled_at.isoformat() if self.cancelled_at else None,
            'refundedAt': self.refunded_at.isoformat() if self.refunded_at else None,
            'canBeRefunded': self.can_be_refunded(),
            'isSuccessful': self.is_successful(),
            'isPending': self.is_pending(),
            'isFailed': self.is_failed(),
            'isRefunded': self.is_refunded(),
            'isPartiallyRefunded': self.is_partially_refunded()
        }
