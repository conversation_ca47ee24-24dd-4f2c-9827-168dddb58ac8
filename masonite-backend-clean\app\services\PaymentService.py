"""Payment Service for handling Razorpay payment operations"""

import razorpay
import hashlib
import hmac
import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Union
from masonite.configuration import config
from app.models.Payment import Payment, PaymentStatus
from app.models.PaymentRefund import PaymentRefund, RefundStatus


class PaymentService:
    """Comprehensive payment service for Razorpay integration"""

    def __init__(self):
        """Initialize Razorpay client"""
        self.razorpay_client = razorpay.Client(
            auth=(
                config('services.razorpay.key_id', 'rzp_test_V1lTfJTbc1xDV7'),
                config('services.razorpay.key_secret', 'your_secret_key')
            )
        )
        self.webhook_secret = config('services.razorpay.webhook_secret', 'your_webhook_secret')

    def create_order(self, amount: float, currency: str, user_id: int, description: str = None) -> Dict:
        """
        Create a new payment order with Razorpay

        Args:
            amount: Payment amount
            currency: Currency code (INR, USD)
            user_id: User ID making the payment
            description: Payment description

        Returns:
            Dict containing order details
        """
        try:
            # Convert amount to smallest currency unit (paise for INR, cents for USD)
            amount_in_smallest_unit = int(amount * 100)

            # Create order with Razorpay
            order_data = {
                'amount': amount_in_smallest_unit,
                'currency': currency,
                'payment_capture': 1,  # Auto capture
                'notes': {
                    'user_id': str(user_id),
                    'description': description or 'Payment for services'
                }
            }

            razorpay_order = self.razorpay_client.order.create(order_data)

            # Create payment record in database
            payment = Payment.create({
                'user_id': user_id,
                'provider': 'razorpay',
                'razorpay_order_id': razorpay_order['id'],
                'amount': amount,
                'currency': currency,
                'status': PaymentStatus.PENDING.value,
                'description': description,
                'metadata': {
                    'razorpay_order': razorpay_order,
                    'created_via': 'api'
                }
            })

            return {
                'orderId': razorpay_order['id'],
                'amount': amount,
                'currency': currency,
                'key': config('services.razorpay.key_id', 'rzp_test_V1lTfJTbc1xDV7'),
                'paymentId': payment.id,
                'description': description,
                'status': 'created'
            }

        except Exception as e:
            print(f"❌ Create order error: {str(e)}")
            raise Exception(f"Failed to create payment order: {str(e)}")

    def verify_payment(self, order_id: str, payment_id: str, signature: str) -> bool:
        """
        Verify payment signature from Razorpay

        Args:
            order_id: Razorpay order ID
            payment_id: Razorpay payment ID
            signature: Payment signature

        Returns:
            bool: True if verification successful
        """
        try:
            # Find payment by order ID
            payment = Payment.where('razorpay_order_id', order_id).first()
            if not payment:
                print(f"❌ Payment not found for order ID: {order_id}")
                return False

            # Verify signature with Razorpay
            params = {
                'razorpay_order_id': order_id,
                'razorpay_payment_id': payment_id,
                'razorpay_signature': signature
            }

            # This will raise an exception if signature is invalid
            self.razorpay_client.utility.verify_payment_signature(params)

            # Update payment as successful
            payment.mark_as_paid(payment_id, signature)
            payment.webhook_verified = True
            payment.save()

            print(f"✅ Payment verified successfully: {payment_id}")
            return True

        except Exception as e:
            print(f"❌ Payment verification failed: {str(e)}")
            # Mark payment as failed
            if 'payment' in locals():
                payment.mark_as_failed(f"Signature verification failed: {str(e)}")
            return False

    def get_payment_status(self, order_id: str) -> Optional[Payment]:
        """
        Get payment status by order ID

        Args:
            order_id: Razorpay order ID

        Returns:
            Payment object or None
        """
        try:
            payment = Payment.where('razorpay_order_id', order_id).first()
            return payment
        except Exception as e:
            print(f"❌ Get payment status error: {str(e)}")
            return None

    def get_user_payments(self, user_id: int, limit: int = 50, offset: int = 0,
                         status_filter: str = None, from_date: str = None, to_date: str = None) -> List[Payment]:
        """
        Get all payments for a user with pagination and filtering

        Args:
            user_id: User ID
            limit: Number of payments to return
            offset: Offset for pagination
            status_filter: Filter by payment status
            from_date: Filter from date (YYYY-MM-DD)
            to_date: Filter to date (YYYY-MM-DD)

        Returns:
            List of Payment objects
        """
        try:
            query = Payment.where('user_id', user_id)

            # Apply status filter
            if status_filter:
                query = query.where('status', status_filter)

            # Apply date filters
            if from_date:
                query = query.where('created_at', '>=', from_date)
            if to_date:
                query = query.where('created_at', '<=', to_date + ' 23:59:59')

            payments = (query.order_by('created_at', 'desc')
                       .limit(limit)
                       .offset(offset)
                       .get())
            return payments
        except Exception as e:
            print(f"❌ Get user payments error: {str(e)}")
            return []

    def get_user_payments_count(self, user_id: int, status_filter: str = None,
                               from_date: str = None, to_date: str = None) -> int:
        """Get total count of user payments with filters"""
        try:
            query = Payment.where('user_id', user_id)

            if status_filter:
                query = query.where('status', status_filter)
            if from_date:
                query = query.where('created_at', '>=', from_date)
            if to_date:
                query = query.where('created_at', '<=', to_date + ' 23:59:59')

            return query.count()
        except Exception as e:
            print(f"❌ Get user payments count error: {str(e)}")
            return 0

    def get_user_refunds(self, user_id: int, limit: int = 50, offset: int = 0) -> List:
        """Get user refunds with pagination"""
        try:
            # Get refunds for user's payments
            refunds = (PaymentRefund.join('payments', 'payment_refunds.payment_id', '=', 'payments.id')
                      .where('payments.user_id', user_id)
                      .select('payment_refunds.*')
                      .order_by('payment_refunds.created_at', 'desc')
                      .limit(limit)
                      .offset(offset)
                      .get())
            return refunds
        except Exception as e:
            print(f"❌ Get user refunds error: {str(e)}")
            return []

    def get_user_refunds_count(self, user_id: int) -> int:
        """Get total count of user refunds"""
        try:
            count = (PaymentRefund.join('payments', 'payment_refunds.payment_id', '=', 'payments.id')
                    .where('payments.user_id', user_id)
                    .count())
            return count
        except Exception as e:
            print(f"❌ Get user refunds count error: {str(e)}")
            return 0

    def get_payment_analytics(self, user_id: int, period: str = '30d') -> Dict:
        """
        Get payment analytics for a user

        Args:
            user_id: User ID
            period: Time period (7d, 30d, 90d, 1y)

        Returns:
            Dict containing analytics data
        """
        try:
            from datetime import datetime, timedelta

            # Calculate date range
            now = datetime.now()
            if period == '7d':
                start_date = now - timedelta(days=7)
            elif period == '30d':
                start_date = now - timedelta(days=30)
            elif period == '90d':
                start_date = now - timedelta(days=90)
            elif period == '1y':
                start_date = now - timedelta(days=365)
            else:
                start_date = now - timedelta(days=30)

            # Get payments in period
            payments = (Payment.where('user_id', user_id)
                       .where('created_at', '>=', start_date)
                       .get())

            # Calculate analytics
            total_payments = len(payments)
            successful_payments = [p for p in payments if p.is_successful()]
            failed_payments = [p for p in payments if p.is_failed()]
            pending_payments = [p for p in payments if p.is_pending()]
            refunded_payments = [p for p in payments if p.is_refunded() or p.is_partially_refunded()]

            total_amount = sum(float(p.amount) for p in successful_payments)
            total_refunded = sum(float(p.refunded_amount or 0) for p in payments)

            # Payment method breakdown
            payment_methods = {}
            for payment in successful_payments:
                method = payment.payment_method or 'unknown'
                payment_methods[method] = payment_methods.get(method, 0) + 1

            # Daily breakdown for charts
            daily_stats = {}
            for payment in payments:
                date_key = payment.created_at.strftime('%Y-%m-%d') if payment.created_at else 'unknown'
                if date_key not in daily_stats:
                    daily_stats[date_key] = {'count': 0, 'amount': 0, 'successful': 0}
                daily_stats[date_key]['count'] += 1
                if payment.is_successful():
                    daily_stats[date_key]['amount'] += float(payment.amount)
                    daily_stats[date_key]['successful'] += 1

            return {
                'summary': {
                    'totalPayments': total_payments,
                    'successfulPayments': len(successful_payments),
                    'failedPayments': len(failed_payments),
                    'pendingPayments': len(pending_payments),
                    'refundedPayments': len(refunded_payments),
                    'totalAmount': total_amount,
                    'totalRefunded': total_refunded,
                    'netAmount': total_amount - total_refunded,
                    'successRate': (len(successful_payments) / total_payments * 100) if total_payments > 0 else 0
                },
                'paymentMethods': payment_methods,
                'dailyStats': daily_stats,
                'period': {
                    'from': start_date.isoformat(),
                    'to': now.isoformat(),
                    'days': (now - start_date).days
                }
            }

        except Exception as e:
            print(f"❌ Get payment analytics error: {str(e)}")
            return {
                'summary': {},
                'paymentMethods': {},
                'dailyStats': {},
                'period': {}
            }

    def refund_payment(self, payment_id: str, amount: Optional[float] = None, reason: str = None) -> bool:
        """
        Process payment refund

        Args:
            payment_id: Razorpay payment ID
            amount: Refund amount (None for full refund)
            reason: Refund reason

        Returns:
            bool: True if refund successful
        """
        try:
            # Find payment by Razorpay payment ID
            payment = Payment.where('razorpay_payment_id', payment_id).first()
            if not payment:
                print(f"❌ Payment not found for payment ID: {payment_id}")
                return False

            if not payment.can_be_refunded():
                print(f"❌ Payment cannot be refunded: {payment_id}")
                return False

            # Determine refund amount
            refund_amount = amount if amount is not None else float(payment.remaining_refund_amount())

            if refund_amount <= 0 or refund_amount > float(payment.remaining_refund_amount()):
                print(f"❌ Invalid refund amount: {refund_amount}")
                return False

            # Create refund record
            refund = PaymentRefund.create({
                'payment_id': payment.id,
                'amount': refund_amount,
                'status': RefundStatus.PENDING.value,
                'reason': reason or 'Refund requested',
                'metadata': {
                    'refund_type': 'partial' if amount is not None else 'full',
                    'original_amount': float(payment.amount),
                    'remaining_before_refund': float(payment.remaining_refund_amount())
                }
            })

            # Process refund with Razorpay
            refund_amount_in_smallest_unit = int(refund_amount * 100)
            razorpay_refund = self.razorpay_client.payment.refund(
                payment_id,
                refund_amount_in_smallest_unit
            )

            # Update refund record
            refund.mark_as_processed(razorpay_refund['id'])

            # Update payment refund amount
            payment.add_refund_amount(Decimal(str(refund_amount)))

            print(f"✅ Refund processed successfully: {razorpay_refund['id']}")
            return True

        except Exception as e:
            print(f"❌ Refund processing failed: {str(e)}")
            # Mark refund as failed if it was created
            if 'refund' in locals():
                refund.mark_as_failed(str(e))
            return False

    def handle_webhook(self, payload: Dict, signature: str) -> bool:
        """
        Handle Razorpay webhook events

        Args:
            payload: Webhook payload
            signature: Webhook signature

        Returns:
            bool: True if webhook processed successfully
        """
        try:
            # Verify webhook signature
            if not self._verify_webhook_signature(payload, signature):
                print("❌ Webhook signature verification failed")
                return False

            event = payload.get('event')
            entity = payload.get('payload', {}).get('payment', {}).get('entity', {})

            print(f"📨 Processing webhook event: {event}")

            if event == 'payment.captured':
                return self._handle_payment_captured(entity)
            elif event == 'payment.failed':
                return self._handle_payment_failed(entity)
            elif event == 'refund.created':
                return self._handle_refund_created(payload.get('payload', {}).get('refund', {}).get('entity', {}))
            elif event == 'refund.processed':
                return self._handle_refund_processed(payload.get('payload', {}).get('refund', {}).get('entity', {}))
            else:
                print(f"⚠️ Unhandled webhook event: {event}")
                return True  # Return True for unknown events to avoid retries

        except Exception as e:
            print(f"❌ Webhook processing error: {str(e)}")
            return False

    def _verify_webhook_signature(self, payload: Dict, signature: str) -> bool:
        """Verify webhook signature"""
        try:
            import hmac
            import hashlib
            import json

            payload_string = json.dumps(payload, separators=(',', ':'))
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload_string.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            return hmac.compare_digest(expected_signature, signature)
        except Exception as e:
            print(f"❌ Signature verification error: {str(e)}")
            return False

    def _handle_payment_captured(self, entity: Dict) -> bool:
        """Handle payment captured webhook"""
        try:
            order_id = entity.get('order_id')
            payment_id = entity.get('id')

            payment = Payment.where('razorpay_order_id', order_id).first()
            if payment and payment.is_pending():
                payment.mark_as_paid(payment_id)
                payment.webhook_verified = True
                payment.save()
                print(f"✅ Payment marked as paid via webhook: {payment_id}")

            return True
        except Exception as e:
            print(f"❌ Handle payment captured error: {str(e)}")
            return False

    def _handle_payment_failed(self, entity: Dict) -> bool:
        """Handle payment failed webhook"""
        try:
            order_id = entity.get('order_id')
            error_description = entity.get('error_description', 'Payment failed')

            payment = Payment.where('razorpay_order_id', order_id).first()
            if payment and payment.is_pending():
                payment.mark_as_failed(error_description)
                print(f"✅ Payment marked as failed via webhook: {order_id}")

            return True
        except Exception as e:
            print(f"❌ Handle payment failed error: {str(e)}")
            return False

    def _handle_refund_created(self, entity: Dict) -> bool:
        """Handle refund created webhook"""
        try:
            payment_id = entity.get('payment_id')
            refund_id = entity.get('id')

            # Find refund record and update with Razorpay refund ID
            payment = Payment.where('razorpay_payment_id', payment_id).first()
            if payment:
                refund = PaymentRefund.where('payment_id', payment.id).where('razorpay_refund_id', None).first()
                if refund:
                    refund.razorpay_refund_id = refund_id
                    refund.save()
                    print(f"✅ Refund updated with Razorpay ID: {refund_id}")

            return True
        except Exception as e:
            print(f"❌ Handle refund created error: {str(e)}")
            return False

    def _handle_refund_processed(self, entity: Dict) -> bool:
        """Handle refund processed webhook"""
        try:
            refund_id = entity.get('id')

            refund = PaymentRefund.where('razorpay_refund_id', refund_id).first()
            if refund and refund.is_pending():
                refund.mark_as_processed()
                print(f"✅ Refund marked as processed via webhook: {refund_id}")

            return True
        except Exception as e:
            print(f"❌ Handle refund processed error: {str(e)}")
            return False