"""CreatePaymentRefundsTable Migration."""

from masoniteorm.migrations import Migration


class CreatePaymentRefundsTable(Migration):
    def up(self):
        """
        Run the migrations.
        """
        with self.schema.create("payment_refunds") as table:
            table.increments("id")

            # Payment relationship
            table.integer("payment_id").unsigned()
            table.foreign("payment_id").references("id").on("payments").on_delete("cascade")

            # Refund details
            table.string("razorpay_refund_id", 100).nullable()
            table.decimal("amount", 10, 2)
            table.enum("status", ["pending", "processed", "failed", "cancelled"]).default("pending")
            table.text("reason").nullable()
            table.text("notes").nullable()

            # Processing information
            table.integer("processed_by").unsigned().nullable()  # Admin user who processed
            table.timestamp("processed_at").nullable()
            table.text("failure_reason").nullable()
            table.json("metadata").nullable()

            # Standard timestamps
            table.timestamps()

            # Indexes
            table.index("payment_id")
            table.index("razorpay_refund_id")
            table.index("status")

    def down(self):
        """
        Revert the migrations.
        """
        self.schema.drop("payment_refunds")
